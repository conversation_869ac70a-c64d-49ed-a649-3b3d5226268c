import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Breadcrumbs as MuiBreadcrumbs,
  Typography,
  Box,
  Chip
} from '@mui/material';
import {
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon
} from '@mui/icons-material';

const Breadcrumbs = () => {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter((x) => x);

  // Map of route paths to display names
  const routeNameMap = {
    users: 'Users',
    products: 'Products',
    orders: 'Orders',
    settings: 'Settings',
    new: 'New',
    edit: 'Edit'
  };

  // Get display name for a path segment
  const getDisplayName = (pathname, index) => {
    // If it's an ID (usually at index 1 in /:resource/:id pattern)
    if (index === 1 && pathname.length > 20) {
      return `ID: ${pathname.substring(0, 6)}...`;
    }
    
    // Use the map or capitalize the first letter
    return routeNameMap[pathname] || 
      pathname.charAt(0).toUpperCase() + pathname.slice(1);
  };

  return (
    <Box className="py-2 px-6 bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 animate-slide-in">
      <MuiBreadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
      >
        <Link 
          to="/"
          className="flex items-center text-primary-600 dark:text-primary-400 hover:underline"
        >
          <HomeIcon fontSize="small" className="mr-1" />
          <Typography variant="body2" className="font-medium">
            Home
          </Typography>
        </Link>
        
        {pathnames.map((pathname, index) => {
          const routeTo = `/${pathnames.slice(0, index + 1).join('/')}`;
          const isLast = index === pathnames.length - 1;
          
          // Special case for "new" and "edit" which are actions, not resources
          const isAction = pathname === 'new' || pathname === 'edit';
          
          // If it's the last item, render as text
          if (isLast) {
            return (
              <Box key={pathname} className="flex items-center">
                {isAction ? (
                  <Chip 
                    label={getDisplayName(pathname, index)}
                    size="small"
                    color={pathname === 'new' ? 'primary' : 'secondary'}
                    className="animate-bounce-in"
                  />
                ) : (
                  <Typography 
                    variant="body2" 
                    color="text.primary"
                    className="font-medium"
                  >
                    {getDisplayName(pathname, index)}
                  </Typography>
                )}
              </Box>
            );
          }
          
          // Otherwise, render as link
          return (
            <Link
              key={pathname}
              to={routeTo}
              className="text-primary-600 dark:text-primary-400 hover:underline"
            >
              <Typography variant="body2" className="font-medium">
                {getDisplayName(pathname, index)}
              </Typography>
            </Link>
          );
        })}
      </MuiBreadcrumbs>
    </Box>
  );
};

export default Breadcrumbs;
