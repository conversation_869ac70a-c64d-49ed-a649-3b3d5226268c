import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Box,
  useTheme,
  Avatar,
  Menu,
  MenuItem,
  Tooltip,
  Divider,
  Badge,
  InputBase,
  Button,
  ListItemIcon,
  ClickAwayListener,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  useMediaQuery,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  AccountCircle,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Search as SearchIcon,
  Help as HelpIcon,
  Person as PersonIcon,
  Clear as ClearIcon,
  Close as CloseIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Inventory as InventoryIcon,
  ShoppingCart as OrdersIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useSearch } from '../../contexts/SearchContext';
import { useToast } from '../../components/ui/Toast';
import SearchResults from '../search/SearchResults';

const Header = ({ toggleTheme, toggleSidebar, sidebarOpen }) => {
  const theme = useTheme();
  const { currentUser, logout } = useAuth();
  const toast = useToast();
  const searchInputRef = useRef(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobileSearchOpen, setMobileSearchOpen] = useState(false);

  // Check if screen is mobile
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallMobile = useMediaQuery('(max-width:480px)');

  // Use search context
  const {
    searchQuery,
    performSearch,
    clearSearch,
    showResults,
    setShowResults
  } = useSearch();

  const [localSearchQuery, setLocalSearchQuery] = useState('');

  // Sync local search query with context
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationsMenu = (event) => {
    setNotificationsAnchorEl(event.currentTarget);
  };

  const handleNotificationsClose = () => {
    setNotificationsAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleClose();
    toast.success('Logged out successfully');
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setLocalSearchQuery(value);

    // If query is empty, clear search results
    if (!value.trim()) {
      clearSearch();
      return;
    }

    // Show search results when typing
    if (value.length >= 2) {
      setShowResults(true);
      // Debounce search to avoid too many requests
      const debounceTimer = setTimeout(() => {
        performSearch(value);
      }, 300);

      return () => clearTimeout(debounceTimer);
    }
  };

  // Handle search form submission
  const handleSearch = (e) => {
    e.preventDefault();
    if (localSearchQuery.trim()) {
      performSearch(localSearchQuery);
    }
    if (isMobile) {
      setMobileSearchOpen(false);
    }
  };

  // Handle clearing search
  const handleClearSearch = () => {
    clearSearch();
    setLocalSearchQuery('');
    // Focus the search input after clearing
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Handle mobile menu toggle
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Handle mobile search toggle
  const toggleMobileSearch = () => {
    setMobileSearchOpen(!mobileSearchOpen);
    if (!mobileSearchOpen && searchInputRef.current) {
      // Focus the search input when opening
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 100);
    }
  };

  return (
    <>
      <AppBar
        position="fixed"
        elevation={0}
        className="text-white border-b border-neutral-200 dark:border-neutral-700"
        sx={{
          zIndex: theme.zIndex.drawer + 1,
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          backgroundColor: theme.palette.mode === 'dark' ? '#1a2035' : '#1976d2',
          boxShadow:
            theme.palette.mode === 'dark'
              ? '0 4px 20px 0 rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(0, 0, 0, 0.4)'
              : '0 4px 20px 0 rgba(25, 118, 210, 0.14), 0 7px 10px -5px rgba(25, 118, 210, 0.4)',
          ...(sidebarOpen && !isMobile && {
            marginLeft: 70,
            width: `calc(100% - 70px)`,
            transition: theme.transitions.create(['width', 'margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
          }),
          width: isMobile ? '100%' : sidebarOpen ? `calc(100% - 70px)` : '100%',
          marginLeft: isMobile ? 0 : sidebarOpen ? 70 : 0,
        }}
      >
        <Toolbar className="px-2 md:px-4" sx={{ minHeight: { xs: '56px', sm: '64px' } }}>
          {/* Menu button - on mobile it opens the mobile menu, on desktop it toggles sidebar */}
          <IconButton
            color="inherit"
            aria-label={isMobile ? "open mobile menu" : "toggle sidebar"}
            onClick={isMobile ? toggleMobileMenu : toggleSidebar}
            edge="start"
            className="mr-2 md:mr-4 transition-transform duration-300 hover:rotate-180"
            sx={{
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            <MenuIcon />
          </IconButton>

          {/* Logo and title */}
          <Box className="items-center flex">
            <Box
              className="w-8 h-8 md:w-10 md:h-10 mr-2 md:mr-3 rounded-md flex items-center justify-center"
              sx={{
                background: 'white',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
              }}
            >
              <Typography
                className="font-bold text-base md:text-lg"
                sx={{
                  color: theme.palette.mode === 'dark' ? '#1a2035' : '#1976d2',
                  fontWeight: 700,
                }}
              >
                C
              </Typography>
            </Box>
            <Typography
              variant={isMobile ? "subtitle1" : "h6"}
              noWrap
              component="div"
              className="font-bold tracking-wide"
              sx={{
                color: 'white',
                fontWeight: 600,
                letterSpacing: '0.5px',
                fontSize: isSmallMobile ? '0.9rem' : undefined,
              }}
            >
              {isSmallMobile ? "CWA Admin" : "Chinioti Wooden Art"}
            </Typography>
          </Box>

          {/* Desktop Search Bar */}
          <Box
            component="form"
            onSubmit={handleSearch}
            className="ml-6 flex-grow max-w-md hidden md:flex items-center rounded-md px-3 py-1.5 transition-all duration-300"
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.15)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.25)',
              },
              '&:focus-within': {
                backgroundColor: 'rgba(255, 255, 255, 0.3)',
                boxShadow: '0 0 0 2px rgba(255, 255, 255, 0.2)',
              },
              position: 'relative',
            }}
          >
            <SearchIcon
              className="mr-2"
              fontSize="small"
              sx={{ color: 'rgba(255, 255, 255, 0.8)' }}
            />
            <InputBase
              inputRef={!isMobile ? searchInputRef : undefined}
              className="header-search-input"
              placeholder="Search products, users, orders..."
              value={localSearchQuery}
              onChange={handleSearchChange}
              sx={{
                fontSize: '0.95rem',
                color: 'white',
                '& ::placeholder': {
                  color: 'rgba(255, 255, 255, 0.7)',
                  opacity: 1,
                },
                flexGrow: 1,
              }}
            />
            {localSearchQuery && (
              <IconButton
                aria-label="clear search"
                size="small"
                onClick={handleClearSearch}
                sx={{
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  },
                }}
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            )}
            <IconButton
              type="submit"
              aria-label="search"
              size="small"
              sx={{
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              <SearchIcon fontSize="small" />
            </IconButton>

            {/* Search Results Dropdown */}
            <SearchResults />
          </Box>

          <Box className="flex-grow md:flex-grow-0" />

          {/* Mobile search button */}
          {isMobile && (
            <IconButton
              color="inherit"
              aria-label="search"
              onClick={toggleMobileSearch}
              size="small"
              sx={{
                color: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                mr: 1,
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                },
              }}
            >
              <SearchIcon fontSize="small" />
            </IconButton>
          )}

          <Box className="flex items-center space-x-1 md:space-x-3">
            {/* Help Button - hide on small mobile */}
            {!isSmallMobile && (
              <Tooltip title="Help & Documentation">
                <IconButton
                  color="inherit"
                  onClick={() => toast.info('Help documentation is coming soon!')}
                  size="small"
                  sx={{
                    color: 'white',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            {/* Notifications */}
            <Tooltip title="Notifications">
              <IconButton
                color="inherit"
                onClick={handleNotificationsMenu}
                size="small"
                sx={{
                  color: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                <Badge
                  badgeContent={3}
                  sx={{
                    '& .MuiBadge-badge': {
                      backgroundColor:
                        theme.palette.mode === 'dark' ? '#ff4f5e' : '#f44336',
                      color: 'white',
                      fontSize: '0.6rem',
                      height: '16px',
                      minWidth: '16px',
                    },
                  }}
                >
                  <NotificationsIcon fontSize="small" />
                </Badge>
              </IconButton>
            </Tooltip>
          <Menu
            id="notifications-menu"
            anchorEl={notificationsAnchorEl}
            keepMounted
            open={Boolean(notificationsAnchorEl)}
            onClose={handleNotificationsClose}
            PaperProps={{
              elevation: 3,
              className: 'mt-2 rounded-lg animate-slide-in-down',
              sx: {
                minWidth: 320,
                overflow: 'hidden',
                border: '1px solid',
                borderColor:
                  theme.palette.mode === 'dark'
                    ? 'rgba(255,255,255,0.1)'
                    : 'rgba(0,0,0,0.1)',
                boxShadow:
                  theme.palette.mode === 'dark'
                    ? '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2)'
                    : '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)',
                borderRadius: '8px',
                '& .MuiList-root': {
                  padding: '0',
                },
                '& .MuiDivider-root': {
                  margin: '0',
                },
              },
            }}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <Box
              sx={{
                px: 4,
                py: 3,
                backgroundColor:
                  theme.palette.mode === 'dark' ? '#1a2035' : '#1976d2',
                color: 'white',
              }}
            >
              <Typography
                variant="subtitle1"
                className="font-bold flex items-center"
              >
                <NotificationsIcon className="mr-2" fontSize="small" />
                Notifications
              </Typography>
            </Box>
            <Divider />
            <MenuItem
              onClick={handleNotificationsClose}
              sx={{
                py: 1.5,
                '&:hover': {
                  backgroundColor:
                    theme.palette.mode === 'dark'
                      ? 'rgba(25, 118, 210, 0.08)'
                      : 'rgba(25, 118, 210, 0.04)',
                },
                transition: 'all 0.2s ease',
              }}
            >
              <Box className="flex flex-col">
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 500,
                    color:
                      theme.palette.mode === 'dark' ? '#1976d2' : '#1565c0',
                  }}
                >
                  New order received
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color:
                      theme.palette.mode === 'dark'
                        ? 'rgba(255,255,255,0.6)'
                        : 'rgba(0,0,0,0.6)',
                  }}
                >
                  Order #12345 - 10 minutes ago
                </Typography>
              </Box>
            </MenuItem>
            <MenuItem
              onClick={handleNotificationsClose}
              sx={{
                py: 1.5,
                '&:hover': {
                  backgroundColor:
                    theme.palette.mode === 'dark'
                      ? 'rgba(244, 67, 54, 0.08)'
                      : 'rgba(244, 67, 54, 0.04)',
                },
                transition: 'all 0.2s ease',
              }}
            >
              <Box className="flex flex-col">
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 500,
                    color:
                      theme.palette.mode === 'dark' ? '#f44336' : '#d32f2f',
                  }}
                >
                  Product stock low
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color:
                      theme.palette.mode === 'dark'
                        ? 'rgba(255,255,255,0.6)'
                        : 'rgba(0,0,0,0.6)',
                  }}
                >
                  Wooden Chair - 1 hour ago
                </Typography>
              </Box>
            </MenuItem>
            <MenuItem
              onClick={handleNotificationsClose}
              sx={{
                py: 1.5,
                '&:hover': {
                  backgroundColor:
                    theme.palette.mode === 'dark'
                      ? 'rgba(76, 175, 80, 0.08)'
                      : 'rgba(76, 175, 80, 0.04)',
                },
                transition: 'all 0.2s ease',
              }}
            >
              <Box className="flex flex-col">
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 500,
                    color:
                      theme.palette.mode === 'dark' ? '#4caf50' : '#388e3c',
                  }}
                >
                  New user registered
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color:
                      theme.palette.mode === 'dark'
                        ? 'rgba(255,255,255,0.6)'
                        : 'rgba(0,0,0,0.6)',
                  }}
                >
                  John Doe - 3 hours ago
                </Typography>
              </Box>
            </MenuItem>
            <Divider />
            <Box
              sx={{
                p: 2,
                textAlign: 'center',
                backgroundColor:
                  theme.palette.mode === 'dark'
                    ? 'rgba(26, 32, 53, 0.3)'
                    : 'rgba(25, 118, 210, 0.03)',
              }}
            >
              <Button
                variant="contained"
                size="small"
                onClick={handleNotificationsClose}
                sx={{
                  borderRadius: '20px',
                  textTransform: 'none',
                  boxShadow: 'none',
                  '&:hover': {
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                  },
                }}
              >
                View All Notifications
              </Button>
            </Box>
          </Menu>

          {/* Theme Toggle - hide on small mobile */}
          {!isSmallMobile && (
            <Tooltip
              title={`Switch to ${
                theme.palette.mode === 'dark' ? 'light' : 'dark'
              } mode`}
            >
              <IconButton
                color="inherit"
                onClick={toggleTheme}
                size="small"
                sx={{
                  color: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                {theme.palette.mode === 'dark' ? (
                  <LightModeIcon fontSize="small" />
                ) : (
                  <DarkModeIcon fontSize="small" />
                )}
              </IconButton>
            </Tooltip>
          )}

          {/* User Menu */}
          <Box className="ml-3">
            <Tooltip title="Account settings">
              <IconButton
                aria-label="account of current user"
                aria-controls="menu-appbar"
                aria-haspopup="true"
                onClick={handleMenu}
                color="inherit"
                sx={{
                  padding: 0,
                  border: '2px solid white',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                {currentUser?.name ? (
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: 'white',
                      color:
                        theme.palette.mode === 'dark' ? '#1a2035' : '#1976d2',
                      fontWeight: 'bold',
                    }}
                  >
                    {currentUser.name.charAt(0).toUpperCase()}
                  </Avatar>
                ) : (
                  <AccountCircle sx={{ color: 'white' }} />
                )}
              </IconButton>
            </Tooltip>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleClose}
              PaperProps={{
                elevation: 3,
                className: 'mt-2 rounded-lg animate-slide-in-down',
                sx: {
                  minWidth: 240,
                  overflow: 'hidden',
                  border: '1px solid',
                  borderColor:
                    theme.palette.mode === 'dark'
                      ? 'rgba(255,255,255,0.1)'
                      : 'rgba(0,0,0,0.1)',
                  boxShadow:
                    theme.palette.mode === 'dark'
                      ? '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2)'
                      : '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)',
                  borderRadius: '8px',
                  '& .MuiList-root': {
                    padding: '0',
                  },
                  '& .MuiDivider-root': {
                    margin: '0',
                  },
                },
              }}
              transformOrigin={{ horizontal: 'right', vertical: 'top' }}
              anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
              {currentUser && (
                <Box
                  sx={{
                    px: 4,
                    py: 4,
                    backgroundColor:
                      theme.palette.mode === 'dark' ? '#1a2035' : '#1976d2',
                    color: 'white',
                  }}
                >
                  <Box className="flex items-center">
                    <Avatar
                      sx={{
                        backgroundColor: 'white',
                        color:
                          theme.palette.mode === 'dark' ? '#1a2035' : '#1976d2',
                        fontWeight: 'bold',
                        width: 48,
                        height: 48,
                        marginRight: 2,
                        boxShadow: '0 3px 5px rgba(0,0,0,0.2)',
                      }}
                    >
                      {currentUser.name?.charAt(0).toUpperCase() || 'A'}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle1" className="font-medium">
                        {currentUser.name || 'Admin User'}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: 'rgba(255,255,255,0.8)' }}
                      >
                        {currentUser.email || '<EMAIL>'}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{ color: 'rgba(255,255,255,0.7)' }}
                      >
                        Administrator
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              )}
              <Divider />
              <MenuItem
                onClick={handleClose}
                component={Link}
                to="/profile"
                sx={{
                  py: 1.5,
                  '&:hover': {
                    backgroundColor:
                      theme.palette.mode === 'dark'
                        ? 'rgba(25, 118, 210, 0.08)'
                        : 'rgba(25, 118, 210, 0.04)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                <ListItemIcon>
                  <PersonIcon
                    fontSize="small"
                    sx={{
                      color:
                        theme.palette.mode === 'dark' ? '#1976d2' : '#1565c0',
                    }}
                  />
                </ListItemIcon>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  My Profile
                </Typography>
              </MenuItem>
              <MenuItem
                onClick={handleClose}
                component={Link}
                to="/settings"
                sx={{
                  py: 1.5,
                  '&:hover': {
                    backgroundColor:
                      theme.palette.mode === 'dark'
                        ? 'rgba(25, 118, 210, 0.08)'
                        : 'rgba(25, 118, 210, 0.04)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                <ListItemIcon>
                  <SettingsIcon
                    fontSize="small"
                    sx={{
                      color:
                        theme.palette.mode === 'dark' ? '#1976d2' : '#1565c0',
                    }}
                  />
                </ListItemIcon>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  Settings
                </Typography>
              </MenuItem>
              <Divider />
              <MenuItem
                onClick={handleLogout}
                sx={{
                  py: 1.5,
                  '&:hover': {
                    backgroundColor:
                      theme.palette.mode === 'dark'
                        ? 'rgba(244, 67, 54, 0.08)'
                        : 'rgba(244, 67, 54, 0.04)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                <ListItemIcon>
                  <LogoutIcon
                    fontSize="small"
                    sx={{
                      color:
                        theme.palette.mode === 'dark' ? '#f44336' : '#d32f2f',
                    }}
                  />
                </ListItemIcon>
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 500,
                    color:
                      theme.palette.mode === 'dark' ? '#f44336' : '#d32f2f',
                  }}
                >
                  Logout
                </Typography>
              </MenuItem>
            </Menu>
          </Box>
        </Box>
      </Toolbar>
    </AppBar>

    {/* Mobile Menu Drawer */}
    <Drawer
      anchor="left"
      open={mobileMenuOpen}
      onClose={toggleMobileMenu}
      sx={{
        '& .MuiDrawer-paper': {
          width: '80%',
          maxWidth: 300,
          boxSizing: 'border-box',
          backgroundColor: theme.palette.mode === 'dark' ? '#1a2035' : '#fff',
          boxShadow: '0 8px 10px -5px rgba(0,0,0,0.2)',
        },
      }}
    >
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            src="/logo.png"
            alt="Logo"
            variant="rounded"
            sx={{ width: 40, height: 40, mr: 2 }}
          />
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            Chinioti Admin
          </Typography>
        </Box>
        <IconButton onClick={toggleMobileMenu}>
          <CloseIcon />
        </IconButton>
      </Box>

      <Divider />

      {currentUser && (
        <Box sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Avatar
              sx={{
                bgcolor: 'primary.main',
                color: 'white',
                width: 40,
                height: 40,
                mr: 2,
              }}
            >
              {currentUser.name?.charAt(0).toUpperCase() || 'A'}
            </Avatar>
            <Box>
              <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                {currentUser.name || 'Admin User'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {currentUser.email || '<EMAIL>'}
              </Typography>
            </Box>
          </Box>
          <Button
            component={Link}
            to="/profile"
            fullWidth
            variant="outlined"
            size="small"
            onClick={toggleMobileMenu}
            sx={{ mt: 1 }}
          >
            View Profile
          </Button>
        </Box>
      )}

      <Divider />

      <List>
        <ListItem disablePadding>
          <ListItemButton component={Link} to="/" onClick={toggleMobileMenu}>
            <ListItemIcon>
              <DashboardIcon color="primary" />
            </ListItemIcon>
            <ListItemText primary="Dashboard" />
          </ListItemButton>
        </ListItem>

        <ListItem disablePadding>
          <ListItemButton component={Link} to="/users" onClick={toggleMobileMenu}>
            <ListItemIcon>
              <PeopleIcon color="primary" />
            </ListItemIcon>
            <ListItemText primary="Users" />
          </ListItemButton>
        </ListItem>

        <ListItem disablePadding>
          <ListItemButton component={Link} to="/products" onClick={toggleMobileMenu}>
            <ListItemIcon>
              <InventoryIcon color="primary" />
            </ListItemIcon>
            <ListItemText primary="Products" />
          </ListItemButton>
        </ListItem>

        <ListItem disablePadding>
          <ListItemButton component={Link} to="/orders" onClick={toggleMobileMenu}>
            <ListItemIcon>
              <OrdersIcon color="primary" />
            </ListItemIcon>
            <ListItemText primary="Orders" />
            <Badge badgeContent="3" color="error" />
          </ListItemButton>
        </ListItem>

        <ListItem disablePadding>
          <ListItemButton component={Link} to="/settings" onClick={toggleMobileMenu}>
            <ListItemIcon>
              <SettingsIcon color="primary" />
            </ListItemIcon>
            <ListItemText primary="Settings" />
          </ListItemButton>
        </ListItem>
      </List>

      <Divider />

      <Box sx={{ p: 2 }}>
        <Button
          fullWidth
          variant="contained"
          color="primary"
          startIcon={theme.palette.mode === 'dark' ? <LightModeIcon /> : <DarkModeIcon />}
          onClick={() => {
            toggleTheme();
            toggleMobileMenu();
          }}
        >
          Switch to {theme.palette.mode === 'dark' ? 'Light' : 'Dark'} Mode
        </Button>

        <Button
          fullWidth
          variant="outlined"
          color="error"
          startIcon={<LogoutIcon />}
          onClick={() => {
            handleLogout();
            toggleMobileMenu();
          }}
          sx={{ mt: 2 }}
        >
          Logout
        </Button>
      </Box>
    </Drawer>

    {/* Mobile Search Drawer */}
    <Drawer
      anchor="top"
      open={mobileSearchOpen}
      onClose={() => setMobileSearchOpen(false)}
      sx={{
        '& .MuiDrawer-paper': {
          boxSizing: 'border-box',
          backgroundColor: theme.palette.mode === 'dark' ? '#1a2035' : '#fff',
          boxShadow: '0 8px 10px -5px rgba(0,0,0,0.2)',
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        <Box
          component="form"
          onSubmit={handleSearch}
          sx={{
            display: 'flex',
            alignItems: 'center',
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
            borderRadius: 1,
            p: 1,
          }}
        >
          <SearchIcon sx={{ mr: 1, color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.5)' }} />
          <InputBase
            inputRef={isMobile ? searchInputRef : undefined}
            placeholder="Search products, users, orders..."
            value={localSearchQuery}
            onChange={handleSearchChange}
            sx={{ ml: 1, flex: 1 }}
            autoFocus
          />
          {localSearchQuery && (
            <IconButton size="small" onClick={handleClearSearch}>
              <ClearIcon fontSize="small" />
            </IconButton>
          )}
          <IconButton type="submit" size="small">
            <SearchIcon fontSize="small" />
          </IconButton>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
          <Button
            variant="outlined"
            size="small"
            onClick={() => setMobileSearchOpen(false)}
            startIcon={<CloseIcon />}
          >
            Close
          </Button>
        </Box>
      </Box>
    </Drawer>
    </>
  );
};

export default Header;
