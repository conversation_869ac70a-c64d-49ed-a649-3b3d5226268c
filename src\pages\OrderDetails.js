import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Person as PersonIcon,
  LocalShipping as ShippingIcon,
  Payment as PaymentIcon
} from '@mui/icons-material';
import { getOrderById, updateOrderStatus } from '../services/orderService';
import { getUserById } from '../services/authService';

const OrderDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [order, setOrder] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [customerLoading, setCustomerLoading] = useState(false);
  const [error, setError] = useState(null);
  const [customerError, setCustomerError] = useState(null);
  const [statusValue, setStatusValue] = useState('');
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  useEffect(() => {
    fetchOrder();
  }, [id]);

  // We don't need to fetch customer data separately as it's included in the order response
  useEffect(() => {
    if (order && order.user) {
      fetchCustomer(order.user);
    }
  }, [order]);

  const fetchOrder = async () => {
    try {
      setLoading(true);
      const response = await getOrderById(id);
      console.log("Order response:", response);

      if (response.status === 'success') {
        const orderData = response.data.order;

        // Log the order data for debugging
        console.log("Order data:", orderData);

        // Log specific sections for debugging
        console.log("Order shipping:", orderData.shipping);
        console.log("Order metadata:", orderData.metadata);
        console.log("Order items:", orderData.items);
        console.log("Order products:", orderData.products);

        if (orderData.items && orderData.items.length > 0) {
          console.log("First item:", orderData.items[0]);
          if (orderData.items[0].product) {
            console.log("First item product:", orderData.items[0].product);
          }
        }

        setOrder(orderData);
        setStatusValue(orderData.status || 'pending');

        // If customer info is in metadata, set it directly
        if (orderData.metadata && orderData.metadata.customer) {
          setCustomer(orderData.metadata.customer);
          setCustomerLoading(false);
        }
      } else {
        setError('Failed to load order details');
      }
    } catch (error) {
      console.error('Error fetching order:', error);
      setError('Failed to load order details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchCustomer = async (userId) => {
    // Only fetch customer if not already available in metadata
    if (order.metadata && order.metadata.customer) {
      return;
    }

    try {
      setCustomerLoading(true);
      const response = await getUserById(userId);

      if (response.success) {
        setCustomer(response.data);
      } else {
        setCustomerError('Failed to load customer details');
      }
    } catch (error) {
      console.error('Error fetching customer:', error);
      setCustomerError('Failed to load customer details');
    } finally {
      setCustomerLoading(false);
    }
  };

  const handleStatusChange = (event) => {
    setStatusValue(event.target.value);
  };

  const handleUpdateStatus = async () => {
    try {
      // Don't update if status hasn't changed
      if (statusValue === order.status) {
        return;
      }

      setLoading(true);
      const response = await updateOrderStatus(id, statusValue);

      if (response.status === 'success') {
        // Update the local order state with the new status
        setOrder({
          ...order,
          status: statusValue
        });

        setSnackbar({
          open: true,
          message: `Order status updated to ${statusValue}`,
          severity: 'success'
        });

        // Refresh the order data to ensure we have the latest state
        fetchOrder();
      } else {
        setSnackbar({
          open: true,
          message: response.message || 'Failed to update order status',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.message || 'Failed to update order status',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Status chip color mapping
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'processing':
        return 'info';
      case 'shipped':
        return 'primary';
      case 'delivered':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Payment method formatter
  const formatPaymentMethod = (method) => {
    switch (method) {
      case 'credit_card':
        return 'Credit Card';
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'cash_on_delivery':
      case 'cash':
        return 'Cash on Delivery';
      default:
        return method ? method.charAt(0).toUpperCase() + method.slice(1) : 'Unknown';
    }
  };

  if (loading && !order) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
        <Button variant="contained" onClick={() => navigate('/orders')} sx={{ mt: 2 }}>
          Back to Orders
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/orders')}
          sx={{ mr: 2 }}
        >
          Back to Orders
        </Button>
        <Typography variant="h4">
          Order Details
        </Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h5">
                Order #{order.id || order._id || 'Unknown'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2" sx={{ mr: 1 }}>
                  {order.date ? new Date(order.date).toLocaleDateString() :
                   order.createdAt ? new Date(order.createdAt).toLocaleDateString() :
                   'Unknown date'}
                </Typography>
                <Chip
                  label={order.status || 'unknown'}
                  color={getStatusColor(order.status)}
                  size="small"
                />
              </Box>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <PersonIcon sx={{ mr: 1 }} />
                      <Typography variant="h6">
                        Customer Information
                      </Typography>
                    </Box>
                    {customerLoading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
                        <CircularProgress size={24} />
                      </Box>
                    ) : customerError ? (
                      <Alert severity="error" sx={{ mt: 1, mb: 2 }}>
                        {customerError}
                      </Alert>
                    ) : order.metadata && order.metadata.customer ? (
                      <>
                        <Typography variant="body1">
                          {order.metadata.customer.firstName} {order.metadata.customer.lastName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {order.metadata.customer.email}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {order.metadata.customer.phone || 'No phone number'}
                        </Typography>
                      </>
                    ) : customer ? (
                      <>
                        <Typography variant="body1">
                          {customer.name || `${customer.firstName || ''} ${customer.lastName || ''}`}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {customer.email}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {customer.phone || 'No phone number'}
                        </Typography>
                      </>
                    ) : (
                      <>
                        <Typography variant="body1">
                          {order.customer || order.metadata?.shippingAddress?.name || 'Customer'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {order.email || 'N/A'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {order.phone || order.shippingAddress?.phone || 'N/A'}
                        </Typography>
                      </>
                    )}
                    {order.user && (
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => navigate(`/users/${order.user}`)}
                        sx={{ mt: 1 }}
                      >
                        View Customer
                      </Button>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <ShippingIcon sx={{ mr: 1 }} />
                      <Typography variant="h6">
                        Shipping Address
                      </Typography>
                    </Box>
                    {order.shipping ? (
                      <>
                        <Typography variant="body1">
                          {order.metadata?.customer?.firstName} {order.metadata?.customer?.lastName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {order.shipping.address || 'N/A'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {order.shipping.city || 'N/A'}{order.shipping.postalCode ? `, ${order.shipping.postalCode}` : ''}
                        </Typography>
                        {order.shipping.country && (
                          <Typography variant="body2" color="text.secondary">
                            {order.shipping.country}
                          </Typography>
                        )}
                        <Typography variant="body2" color="text.secondary">
                          {order.metadata?.customer?.phone || 'N/A'}
                        </Typography>
                      </>
                    ) : order.metadata?.shipping ? (
                      <>
                        {/* <Typography variant="body1">
                          {order.metadata?.shippingAddresss.name || 'N/A'}
                        </Typography> */}
                        <Typography variant="body2" color="text.secondary">
                          {order.metadata?.shipping.address || 'N/A'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {order.metadata?.shipping.city }, {order.metadata?.shipping.postalCode }
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {order.metadata?.shipping.country || 'N/A'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {order.metadata?.shipping.phone || 'N/A'}
                        </Typography>
                      </>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No shipping address information available
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <PaymentIcon sx={{ mr: 1 }} />
                      <Typography variant="h6">
                        Payment Information
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Method: {formatPaymentMethod(order.metadata.paymentMethod)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Subtotal: ${order.metadata.subtotal || 0}
                    </Typography>
                    {order.discount > 0 && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        Discount: -${order.discount}
                      </Typography>
                    )}
                    <Typography variant="body1" sx={{ mt: 1, fontWeight: 'bold' }}>
                      Total: ${order.total || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                      Status:
                      {order.status ? (
                        <Chip
                          label={order.status}
                          color={getStatusColor(order.status)}
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      ) : (
                        <Chip
                          label="unknown"
                          color="default"
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Order Items
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Product</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell align="right">Price</TableCell>
                      <TableCell align="right">Quantity</TableCell>
                      <TableCell align="right">Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {order.products && order.products.length > 0 ? (
                      order.products.map((product) => (
                        <TableRow key={product._id || `product-${Math.random()}`}>
                          <TableCell>
                            <Box
                              component="img"
                              src={
                                product.image ?
                                `/uploads/${product.image}` :
                                product.images?.[0] ?
                                product.images[0] :
                                `https://via.placeholder.com/50?text=${encodeURIComponent(product.name || product.title || 'Product')}`
                              }
                              alt={product.name || 'Product'}
                              sx={{
                                width: 50,
                                height: 50,
                                objectFit: 'cover',
                                borderRadius: 1
                              }}
                              onError={(e) => {
                                e.target.onerror = null;
                                e.target.src = `https://via.placeholder.com/50?text=Product`;
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {product.name || product.title || 'Unknown Product'}
                            </Typography>
                            {product._id && (
                              <Button
                                variant="text"
                                size="small"
                                onClick={() => navigate(`/products/${product._id}`)}
                              >
                                View Product
                              </Button>
                            )}
                          </TableCell>
                          <TableCell align="right">${product.price || 0}</TableCell>
                          <TableCell align="right">1</TableCell>
                          <TableCell align="right">${product.price || 0}</TableCell>
                        </TableRow>
                      ))
                    ) : order.items && order.items.length > 0 ? (
                      order.items.map((item) => (
                        <TableRow key={item.productId || `item-${Math.random()}`}>
                          <TableCell>
                            <Box
                              component="img"
                              src={
                                item.product?.image ? `/uploads/${item.product.image}` :
                                item.product?.images?.[0] ? item.product.images[0] :
                                item.imageUrl ||
                                item.image ? `/uploads/${item.image}` :
                                `https://via.placeholder.com/50?text=${encodeURIComponent(item.product?.title || item.name || 'Product')}`
                              }
                              alt={item.product?.title || item.name || 'Product'}
                              sx={{
                                width: 50,
                                height: 50,
                                objectFit: 'cover',
                                borderRadius: 1
                              }}
                              onError={(e) => {
                                e.target.onerror = null;
                                e.target.src = `https://via.placeholder.com/50?text=Product`;
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {item.product?.title || item.name || 'Unknown Product'}
                            </Typography>
                            {(item.productId || item.product?.id) && (
                              <Button
                                variant="text"
                                size="small"
                                onClick={() => navigate(`/products/${item.productId || item.product?.id}`)}
                              >
                                View Product
                              </Button>
                            )}
                          </TableCell>
                          <TableCell align="right">${item.price || item.product?.price || 0}</TableCell>
                          <TableCell align="right">{item.quantity || 1}</TableCell>
                          <TableCell align="right">${(item.price || item.product?.price || 0) * (item.quantity || 1)}</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} align="center">
                          <Typography variant="body2" color="text.secondary">
                            No items found in this order
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                    {order.subtotal && (
                      <TableRow>
                        <TableCell colSpan={3} />
                        <TableCell align="right">
                          <Typography variant="body2">
                            Subtotal:
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2">
                            ${order.subtotal || 0}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                    {order.discount > 0 && (
                      <TableRow>
                        <TableCell colSpan={3} />
                        <TableCell align="right">
                          <Typography variant="body2">
                            Discount:
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" color="error">
                            -${order.discount || 0}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                    <TableRow>
                      <TableCell colSpan={3} />
                      <TableCell align="right">
                        <Typography variant="subtitle1">
                          Total:
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="subtitle1">
                          ${order.total || 0}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>

            <Box sx={{ mt: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 1, bgcolor: 'background.paper' }}>
              <Typography variant="h6" gutterBottom>
                Order Status Management
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                <FormControl sx={{ minWidth: 200, mr: 2, mb: { xs: 2, md: 0 } }}>
                  <InputLabel>Update Status</InputLabel>
                  <Select
                    value={statusValue}
                    label="Update Status"
                    onChange={handleStatusChange}
                    disabled={loading}
                  >
                    <MenuItem value="pending">Pending</MenuItem>
                    <MenuItem value="processing">Processing</MenuItem>
                    <MenuItem value="shipped">Shipped</MenuItem>
                    <MenuItem value="delivered">Delivered</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                  </Select>
                </FormControl>
                <Button
                  variant="contained"
                  onClick={handleUpdateStatus}
                  disabled={loading || statusValue === order.status}
                  sx={{ minWidth: 150 }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Update Status'}
                </Button>
              </Box>
              {statusValue !== order.status && (
                <Alert severity="info" sx={{ mt: 2 }}>
                  Status will be changed from <strong>{order.status}</strong> to <strong>{statusValue}</strong>
                </Alert>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default OrderDetails;
