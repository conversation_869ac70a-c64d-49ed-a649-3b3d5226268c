import React, { useState, useEffect, memo } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { CssBaseline, ThemeProvider, createTheme } from '@mui/material';
import './App.css';

// Providers
import { AuthProvider } from './contexts/AuthContext';
import { SearchProvider } from './contexts/SearchContext';
import ToastProvider from './components/ui/Toast';
import { ConfirmDialogProvider } from './components/ui/ConfirmDialog';
import ProtectedRoute from './components/ProtectedRoute';

// Layout Components
import Sidebar from './components/layout/Sidebar';
import Header from './components/layout/Header';
import Breadcrumbs from './components/layout/Breadcrumbs';

// Pages
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';
import UserDetails from './pages/UserDetails';
import UserForm from './pages/UserForm';
import Products from './pages/Products';
import ProductDetails from './pages/ProductDetails';
import ProductForm from './pages/ProductForm';
import Orders from './pages/Orders';
import OrderDetails from './pages/OrderDetails';
import Settings from './pages/Settings';
import Profile from './pages/Profile';
import Login from './pages/Login';

function App() {
  const [mode, setMode] = useState('light');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const theme = createTheme({
    palette: {
      mode,
      primary: {
        main: '#1976d2',
      },
      secondary: {
        main: '#dc004e',
      },
    },
  });

  const toggleTheme = () => {
    setMode(prevMode => prevMode === 'light' ? 'dark' : 'light');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Layout wrapper component - memoized to prevent unnecessary re-renders
  const AdminLayout = memo(({ children }) => (
    <div className="app-container">
      <Header
        toggleTheme={toggleTheme}
        toggleSidebar={toggleSidebar}
        sidebarOpen={sidebarOpen}
      />
      <Sidebar open={sidebarOpen} />
      <div className={`content ${sidebarOpen ? '' : 'sidebar-closed'}`}>
        <Breadcrumbs />
        {children}
      </div>
    </div>
  ));

  // Set up theme preference from localStorage
  useEffect(() => {
    const savedMode = localStorage.getItem('theme_mode');
    if (savedMode) {
      setMode(savedMode);
    }
  }, []);

  // Save theme preference to localStorage
  useEffect(() => {
    localStorage.setItem('theme_mode', mode);
  }, [mode]);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <ToastProvider>
          <ConfirmDialogProvider>
            <Router>
              <SearchProvider>
                <Routes>
                  <Route path="/login" element={<Login />} />

                <Route path="/" element={
                  <ProtectedRoute>
                    <AdminLayout>
                      <Dashboard />
                    </AdminLayout>
                  </ProtectedRoute>
                } />

            {/* User Routes */}
            <Route path="/users" element={
              <ProtectedRoute>
                <AdminLayout>
                  <Users />
                </AdminLayout>
              </ProtectedRoute>
            } />

            <Route path="/users/new" element={
              <ProtectedRoute>
                <AdminLayout>
                  <UserForm />
                </AdminLayout>
              </ProtectedRoute>
            } />

            <Route path="/users/:id" element={
              <ProtectedRoute>
                <AdminLayout>
                  <UserDetails />
                </AdminLayout>
              </ProtectedRoute>
            } />

            {/* Product Routes */}
            <Route path="/products" element={
              <ProtectedRoute>
                <AdminLayout>
                  <Products />
                </AdminLayout>
              </ProtectedRoute>
            } />

            <Route path="/products/new" element={
              <ProtectedRoute>
                <AdminLayout>
                  <ProductForm />
                </AdminLayout>
              </ProtectedRoute>
            } />

            <Route path="/products/:id" element={
              <ProtectedRoute>
                <AdminLayout>
                  <ProductDetails />
                </AdminLayout>
              </ProtectedRoute>
            } />

            <Route path="/products/:id/edit" element={
              <ProtectedRoute>
                <AdminLayout>
                  <ProductForm />
                </AdminLayout>
              </ProtectedRoute>
            } />

            {/* Order Routes */}
            <Route path="/orders" element={
              <ProtectedRoute>
                <AdminLayout>
                  <Orders />
                </AdminLayout>
              </ProtectedRoute>
            } />

            <Route path="/orders/:id" element={
              <ProtectedRoute>
                <AdminLayout>
                  <OrderDetails />
                </AdminLayout>
              </ProtectedRoute>
            } />

            {/* Settings Routes */}
            <Route path="/settings" element={
              <ProtectedRoute>
                <AdminLayout>
                  <Settings />
                </AdminLayout>
              </ProtectedRoute>
            } />

            {/* Profile Routes */}
            <Route path="/profile" element={
              <ProtectedRoute>
                <AdminLayout>
                  <Profile />
                </AdminLayout>
              </ProtectedRoute>
            } />

            <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
              </SearchProvider>
            </Router>
          </ConfirmDialogProvider>
        </ToastProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
