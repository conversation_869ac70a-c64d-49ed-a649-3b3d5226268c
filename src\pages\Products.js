import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Alert,
  Snackbar,
  Avatar,
  Tooltip,
  InputAdornment,
  LinearProgress,
  Badge,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Add as AddIcon,
  Inventory as InventoryIcon,
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  Category as CategoryIcon,
  AttachMoney as PriceIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import {
  getProducts,
  getCategories,
  deleteProduct,
} from '../services/productService';
import { ContentLoader, TableRowSkeleton } from '../components/ui/Loading';

const Products = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isSmallMobile = useMediaQuery('(max-width:480px)');

  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalProducts, setTotalProducts] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // Initial data loading
  useEffect(() => {
    fetchCategories();
    fetchProducts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle pagination changes
  useEffect(() => {
    if (products.length > 0) {
      // Only fetch if not the initial load
      fetchProducts();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, rowsPerPage]);

  const fetchCategories = async () => {
    try {
      const response = await getCategories();

      if (response.status === 'success') {
        setCategories(response.data.categories);
      } else {
        console.error('Failed to fetch categories');
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await getProducts({
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm,
        category: categoryFilter,
      });

      console.log('Products response in component:', response);

      if (response.status === 'success') {
        // Handle different API response formats
        if (response.data.products) {
          // Use the products directly - image URLs are already processed in the service
          const productsWithImages = response.data.products;

          setProducts(productsWithImages);
          setTotalProducts(
            response.data.total || response.data.products.length
          );
        } else if (Array.isArray(response.data)) {
          // If the API returns an array directly - image URLs are already processed in the service
          const productsWithImages = response.data;

          setProducts(productsWithImages);
          setTotalProducts(productsWithImages.length);
        } else {
          console.error('Unexpected API response format:', response);
          setError('Unexpected API response format');
        }
      } else {
        setError(response.message || 'Failed to fetch products');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      setError('Failed to fetch products. Please try again.');
      // Set empty products array to avoid undefined errors
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleSearchSubmit = (event) => {
    event.preventDefault();
    setPage(0);
    fetchProducts();
  };

  const handleCategoryFilterChange = (event) => {
    setCategoryFilter(event.target.value);
    setPage(0);
    fetchProducts();
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleDeleteClick = (product) => {
    setProductToDelete(product);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!productToDelete) return;

    try {
      setLoading(true);
      console.log('Deleting product:', productToDelete);

      // Make sure we're using the correct ID field
      const productId = productToDelete.id || productToDelete._id;

      if (!productId) {
        throw new Error('Product ID is missing');
      }

      console.log('Using product ID for deletion:', productId);
      const response = await deleteProduct(productId);
      console.log('Delete response:', response);

      if (response && response.status === 'success') {
        setSnackbar({
          open: true,
          message: `Product "${productToDelete.name}" deleted successfully`,
          severity: 'success',
        });
        // Refresh the product list
        fetchProducts();
      } else {
        // Handle error response from the service
        setSnackbar({
          open: true,
          message: response?.message || 'Failed to delete product',
          severity: 'error',
        });
        console.error('Delete product failed with response:', response);
      }
    } catch (error) {
      console.error('Error in handleDeleteConfirm:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to delete product',
        severity: 'error',
      });
    } finally {
      setDeleteDialogOpen(false);
      setProductToDelete(null);
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box className="p-6 animate-fade-in">
      <Box className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4 sm:gap-0">
        <Box className="flex items-center">
          <InventoryIcon className="text-secondary-600 dark:text-secondary-400 mr-3 text-3xl sm:text-4xl" />
          <div>
            <Typography
              variant="h4"
              className="font-bold bg-clip-text text-transparent bg-gradient-to-r from-secondary-600 to-secondary-800 dark:from-secondary-400 dark:to-secondary-600 text-xl sm:text-2xl md:text-3xl"
            >
              Product Management
            </Typography>
            <Typography
              variant="body2"
              className="text-neutral-500 dark:text-neutral-400 text-xs sm:text-sm"
            >
              Manage your products, add new ones, or edit existing ones.
            </Typography>
          </div>
        </Box>

        <Button
          variant="contained"
          color="secondary"
          startIcon={<AddIcon />}
          onClick={() => navigate('/products/new')}
          className="transition-all duration-300 hover:shadow-lg rounded-full w-full sm:w-auto"
          size={isSmallMobile ? "small" : "medium"}
          sx={{
            background: 'linear-gradient(45deg, #c2185b 30%, #e91e63 90%)',
            boxShadow: '0 3px 5px 2px rgba(233, 30, 99, .3)',
            fontSize: { xs: '0.8rem', sm: '0.875rem' },
            px: { xs: 2, sm: 3 },
            py: { xs: 1, sm: 1.5 },
          }}
        >
          {isSmallMobile ? "Add Product" : "Add New Product"}
        </Button>
      </Box>

      {error && (
        <Alert
          severity="error"
          className="mb-6 rounded-lg shadow-md animate-slide-in"
          action={
            <Button color="inherit" size="small" onClick={() => setError(null)}>
              Dismiss
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      <Paper
        className="p-5 overflow-hidden relative mb-6 animate-slide-in"
        elevation={3}
        sx={{
          borderRadius: '16px',
          border: '1px solid',
          borderColor: 'rgba(236, 72, 153, 0.1)',
        }}
      >
        <Box className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-secondary-400 to-secondary-600"></Box>

        <Grid container spacing={3} className="mb-4">
          <Grid item xs={12} md={6}>
            <form onSubmit={handleSearchSubmit}>
              <TextField
                label="Search Products"
                variant="outlined"
                size="small"
                fullWidth
                value={searchTerm}
                onChange={handleSearch}
                className="bg-white dark:bg-neutral-800 rounded-full shadow-sm"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon className="text-neutral-500 dark:text-neutral-400" />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => {
                          setSearchTerm('');
                          if (searchTerm) fetchProducts();
                        }}
                      >
                        <ClearIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                  className: 'rounded-full',
                  sx: {
                    borderRadius: '9999px',
                    paddingLeft: '8px',
                    '& fieldset': { borderRadius: '9999px' },
                  },
                }}
              />
              <Box className="flex gap-2 mt-2">
                <Button
                  type="submit"
                  variant="contained"
                  color="secondary"
                  disabled={loading}
                  className="rounded-full text-sm px-4"
                  startIcon={<SearchIcon />}
                >
                  Search
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={() => {
                    setSearchTerm('');
                    setCategoryFilter('');
                    fetchProducts();
                  }}
                  disabled={loading}
                  className="rounded-full text-sm"
                  startIcon={<RefreshIcon />}
                >
                  Reset Filters
                </Button>
              </Box>
            </form>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box className="bg-neutral-50 dark:bg-neutral-800/50 p-3 rounded-xl border border-neutral-200 dark:border-neutral-700">
              <Typography
                variant="subtitle2"
                className="mb-2 flex items-center text-secondary-600 dark:text-secondary-400"
              >
                <CategoryIcon fontSize="small" className="mr-1" /> Filter by
                Category
              </Typography>
              <FormControl fullWidth size="small">
                <Select
                  value={categoryFilter}
                  onChange={handleCategoryFilterChange}
                  disabled={loading}
                  displayEmpty
                  className="rounded-full bg-white dark:bg-neutral-800"
                  sx={{
                    borderRadius: '9999px',
                    '& fieldset': { borderRadius: '9999px' },
                  }}
                >
                  <MenuItem value="">
                    <em>All Categories</em>
                  </MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Box className="flex justify-between items-center mt-3">
                <Chip
                  icon={
                    <InventoryIcon
                      className="text-secondary-600 dark:text-secondary-400"
                      fontSize="small"
                    />
                  }
                  label={`Total: ${totalProducts}`}
                  size="small"
                  className="bg-secondary-50 dark:bg-secondary-900/30 text-secondary-700 dark:text-secondary-300 font-medium shadow-sm"
                />

                <Tooltip title="Advanced Filters">
                  <IconButton
                    color="secondary"
                    size="small"
                    className="hover:bg-secondary-50 dark:hover:bg-secondary-900/30"
                  >
                    <FilterListIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
          </Grid>
        </Grid>

        {loading && (
          <Box className="mb-4">
            <LinearProgress
              color="secondary"
              className="rounded-full h-1 animate-pulse"
            />
          </Box>
        )}

        <TableContainer className="rounded-xl overflow-hidden border border-neutral-200 dark:border-neutral-700 shadow-sm">
          <Table>
            <TableHead className="bg-neutral-50 dark:bg-neutral-800">
              <TableRow>
                <TableCell className="font-bold text-neutral-700 dark:text-neutral-300">
                  Product
                </TableCell>
                <TableCell className="font-bold text-neutral-700 dark:text-neutral-300">
                  Category
                </TableCell>
                <TableCell className="font-bold text-neutral-700 dark:text-neutral-300">
                  Price
                </TableCell>
                <TableCell className="font-bold text-neutral-700 dark:text-neutral-300">
                  Inventory
                </TableCell>
                <TableCell className="font-bold text-neutral-700 dark:text-neutral-300">
                  Status
                </TableCell>
                <TableCell
                  className="font-bold text-neutral-700 dark:text-neutral-300"
                  align="center"
                >
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                // Show skeleton loaders while loading
                Array(5)
                  .fill(0)
                  .map((_, index) => (
                    <TableRowSkeleton key={index} columns={6} />
                  ))
              ) : products.length > 0 ? (
                products.map((product) => (
                  <TableRow
                    key={product.id || product._id}
                    hover
                    className="transition-colors duration-150 hover:bg-neutral-50 dark:hover:bg-neutral-800/50 group"
                  >
                    <TableCell>
                      <Box className="flex items-center">
                        <Box
                          component="img"
                          sx={{
                            height: 48,
                            width: 48,
                            objectFit: 'cover',
                            borderRadius: '8px',
                            border: '2px solid',
                            borderColor: 'rgba(236, 72, 153, 0.2)',
                            transition: 'all 0.3s ease',
                            backgroundColor: 'rgba(236, 72, 153, 0.05)',
                          }}
                          className="mr-3 shadow-sm group-hover:scale-110"
                          alt={product.name}
                          src={
                            product.image || product.imageUrl ||
                            `https://via.placeholder.com/48?text=${encodeURIComponent(
                              product.name.charAt(0)
                            )}`
                          }
                          onError={(e) => {
                            console.log(
                              'Image failed to load:',
                              product.image || product.imageUrl
                            );
                            e.target.onerror = null;
                            e.target.src = `https://via.placeholder.com/48?text=${encodeURIComponent(
                              product.name.charAt(0)
                            )}`;
                          }}
                        />
                        <Box>
                          <Typography className="font-medium group-hover:text-secondary-600 dark:group-hover:text-secondary-400 transition-colors duration-300">
                            {product.name}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={product.category || 'Uncategorized'}
                        size="small"
                        className="bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                        icon={<CategoryIcon fontSize="small" />}
                      />
                    </TableCell>
                    <TableCell>
                      <Box className="flex items-center text-secondary-600 dark:text-secondary-400 font-medium">
                        <PriceIcon fontSize="small" className="mr-1" />$
                        {parseFloat(product.price).toFixed(2)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      {product.quantity > 10 ? (
                        <Badge
                          badgeContent={product.quantity}
                          color="primary"
                          max={999}
                          showZero
                        >
                          <Chip
                            label="In Stock"
                            size="small"
                            className="bg-success-100 text-success-700 dark:bg-success-900/30 dark:text-success-300"
                          />
                        </Badge>
                      ) : product.quantity > 0 ? (
                        <Badge
                          badgeContent={product.quantity}
                          color="warning"
                          max={999}
                          showZero
                        >
                          <Chip
                            label="Low Stock"
                            size="small"
                            className="bg-warning-100 text-warning-700 dark:bg-warning-900/30 dark:text-warning-300"
                          />
                        </Badge>
                      ) : (
                        <Badge badgeContent={0} color="error" showZero>
                          <Chip
                            label="Out of Stock"
                            size="small"
                            className="bg-error-100 text-error-700 dark:bg-error-900/30 dark:text-error-300"
                          />
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={product.inStock ? 'Active' : 'Inactive'}
                        color={product.inStock ? 'success' : 'error'}
                        size="small"
                        variant="outlined"
                        className="shadow-sm"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box className="flex justify-center space-x-1">
                        <Tooltip title="View Details">
                          <IconButton
                            color="primary"
                            size="small"
                            onClick={() =>
                              navigate(`/products/${product.id || product._id}`)
                            }
                            className="hover:bg-primary-50 dark:hover:bg-primary-900/30 transition-colors duration-300"
                          >
                            <ViewIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit Product">
                          <IconButton
                            color="secondary"
                            size="small"
                            onClick={() =>
                              navigate(
                                `/products/${product.id || product._id}/edit`
                              )
                            }
                            className="hover:bg-secondary-50 dark:hover:bg-secondary-900/30 transition-colors duration-300"
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Product">
                          <IconButton
                            color="error"
                            size="small"
                            onClick={() => handleDeleteClick(product)}
                            disabled={loading}
                            className="hover:bg-error-50 dark:hover:bg-error-900/30 transition-colors duration-300"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center" className="py-8">
                    <Box className="text-center">
                      <InventoryIcon className="text-neutral-400 dark:text-neutral-600 text-5xl mb-2" />
                      <Typography
                        variant="body1"
                        className="text-neutral-500 dark:text-neutral-400"
                      >
                        No products found
                      </Typography>
                      <Button
                        variant="outlined"
                        color="secondary"
                        className="mt-4 rounded-full w-full sm:w-auto"
                        startIcon={<AddIcon />}
                        onClick={() => navigate('/products/new')}
                        size={isSmallMobile ? "small" : "medium"}
                        sx={{
                          fontSize: { xs: '0.8rem', sm: '0.875rem' },
                          px: { xs: 2, sm: 3 },
                          py: { xs: 1, sm: 1.5 },
                        }}
                      >
                        {isSmallMobile ? "Add Product" : "Add Your First Product"}
                      </Button>
                    </Box>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Box className="flex justify-end mt-4">
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={totalProducts}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            className="shadow-sm border border-neutral-200 dark:border-neutral-700 rounded-lg"
          />
        </Box>
      </Paper>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        PaperProps={{
          elevation: 3,
          className: 'rounded-xl overflow-hidden animate-scale-in',
          sx: {
            minWidth: 320,
            border: '1px solid',
            borderColor: (theme) =>
              theme.palette.mode === 'dark'
                ? 'rgba(255,255,255,0.1)'
                : 'rgba(0,0,0,0.1)',
          },
        }}
      >
        <Box className="bg-error-50 dark:bg-error-900/30 px-6 py-4">
          <Typography
            variant="h6"
            className="font-bold text-error-700 dark:text-error-300 flex items-center"
          >
            <DeleteIcon className="mr-2" /> Confirm Delete
          </Typography>
        </Box>
        <DialogContent className="pt-4">
          <Box className="flex items-center mb-4">
            <Box
              component="img"
              sx={{
                height: 60,
                width: 60,
                objectFit: 'cover',
                borderRadius: '8px',
                border: '2px solid',
                borderColor: 'rgba(239, 68, 68, 0.2)',
                backgroundColor: 'rgba(239, 68, 68, 0.05)',
              }}
              className="mr-3 shadow-sm"
              alt={productToDelete?.name}
              src={
                productToDelete?.image || productToDelete?.imageUrl ||
                `https://via.placeholder.com/60?text=${encodeURIComponent(
                  productToDelete?.name?.charAt(0) || 'P'
                )}`
              }
              onError={(e) => {
                console.log(
                  'Image failed to load in dialog:',
                  productToDelete?.image || productToDelete?.imageUrl
                );
                e.target.onerror = null;
                e.target.src = `https://via.placeholder.com/60?text=${encodeURIComponent(
                  productToDelete?.name?.charAt(0) || 'P'
                )}`;
              }}
            />
            <Box>
              <Typography
                variant="subtitle1"
                className="font-semibold text-error-600 dark:text-error-400"
              >
                {productToDelete?.name}
              </Typography>
              <Typography
                variant="body2"
                className="text-neutral-500 dark:text-neutral-400"
              >
                {productToDelete?.category} - ${productToDelete?.price}
              </Typography>
            </Box>
          </Box>

          <DialogContentText className="text-neutral-700 dark:text-neutral-300">
            Are you sure you want to delete this product?
            <br />
            <br />
            <span className="text-error-600 dark:text-error-400 font-medium">
              This action cannot be undone.
            </span>
          </DialogContentText>
        </DialogContent>
        <DialogActions className="p-4 bg-neutral-50 dark:bg-neutral-800">
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            variant="outlined"
            className="rounded-full"
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            className="rounded-full"
            startIcon={<DeleteIcon />}
            autoFocus
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        className="mb-4 mr-4"
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          className="rounded-lg shadow-md animate-slide-in-up w-full"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Products;
