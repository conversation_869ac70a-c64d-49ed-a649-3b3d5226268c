import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Divider,
  IconButton,
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  ArrowBack as ArrowBackIcon,
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import {
  getProductById,
  createProduct,
  updateProduct,
  getCategories,
} from '../services/productService';

const ProductForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [loading, setLoading] = useState(isEditMode);
  const [error, setError] = useState(null);
  const [categories, setCategories] = useState([]);
  const [formData, setFormData] = useState({
    name: '',
    type: 'regular', // Default type
    category: 'Uncategorized', // Default category
    price: '',
    quantity: '',
    discount: '', // New discount field
    description: '',
    stock: true, // Changed from inStock to match backend model
  });
  const [mainImage, setMainImage] = useState(null);
  const [additionalImages, setAdditionalImages] = useState([]);
  const [mainImagePreview, setMainImagePreview] = useState(null);
  const [additionalImagePreviews, setAdditionalImagePreviews] = useState([]);
  const [formErrors, setFormErrors] = useState({});
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  useEffect(() => {
    fetchCategories();
    if (isEditMode) {
      fetchProduct();
    }
  }, [id, isEditMode]);

  const fetchCategories = async () => {
    try {
      const response = await getCategories();

      if (response.status === 'success') {
        setCategories(response.data.categories);
      } else {
        console.error('Failed to fetch categories');
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchProduct = async () => {
    try {
      console.log('Fetching product with ID:', id);
      const response = await getProductById(id);
      console.log('Product response in form:', response);

      if (response.status === 'success') {
        // Handle different API response formats
        const product = response.data.product || response.data;

        if (!product) {
          throw new Error('Product data not found in API response');
        }

        console.log('Product data:', product);

        setFormData({
          name: product.name || '',
          type: product.type || 'regular',
          category: product.category || '',
          price: (product.price || 0).toString(),
          quantity: (product.quantity || 0).toString(),
          discount: product.discount || '',
          description: product.description || '',
          stock: product.stock !== undefined ? product.stock : true,
        });

        // Set image previews
        if (product.imageUrl) {
          setMainImagePreview(product.imageUrl);
        } else if (product.image) {
          // Handle image field from backend model
          const imageUrl = product.image.startsWith('http')
            ? product.image
            : `${process.env.REACT_APP_API_URL.replace('/api', '')}/uploads/${product.image}`;
          setMainImagePreview(imageUrl);
        }

        // Handle additional images
        if (product.additionalImages && product.additionalImages.length > 0) {
          setAdditionalImagePreviews(product.additionalImages);
        } else if (product.images && product.images.length > 0) {
          // Handle images field from backend model
          const imageUrls = product.images.map(img =>
            img.startsWith('http')
              ? img
              : `${process.env.REACT_APP_API_URL.replace('/api', '')}/uploads/${img}`
          );
          setAdditionalImagePreviews(imageUrls);
        }
      } else {
        setError(response.message || 'Failed to load product details');
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      setError('Failed to load product details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleNumberInputChange = (e) => {
    const { name, value } = e.target;
    // Only allow numbers and decimal point
    if (value === '' || /^[0-9]+(\.[0-9]*)?$/.test(value)) {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleMainImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setMainImage(file);

      // Create a preview
      const reader = new FileReader();
      reader.onload = () => {
        setMainImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAdditionalImagesChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      setAdditionalImages([...additionalImages, ...files]);

      // Create previews
      const newPreviews = [];
      files.forEach((file) => {
        const reader = new FileReader();
        reader.onload = () => {
          newPreviews.push(reader.result);
          if (newPreviews.length === files.length) {
            setAdditionalImagePreviews([
              ...additionalImagePreviews,
              ...newPreviews,
            ]);
          }
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const handleRemoveAdditionalImage = (index) => {
    const newImages = [...additionalImages];
    newImages.splice(index, 1);

    const newPreviews = [...additionalImagePreviews];
    newPreviews.splice(index, 1);

    setAdditionalImages(newImages);
    setAdditionalImagePreviews(newPreviews);
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name) {
      errors.name = 'Product name is required';
    }

    if (!formData.type) {
      errors.type = 'Product type is required';
    }

    if (!formData.category) {
      errors.category = 'Category is required';
    }

    if (!formData.price) {
      errors.price = 'Price is required';
    } else if (isNaN(formData.price) || parseFloat(formData.price) <= 0) {
      errors.price = 'Price must be a positive number';
    }

    if (!formData.quantity) {
      errors.quantity = 'Quantity is required';
    } else if (isNaN(formData.quantity) || parseInt(formData.quantity) < 0) {
      errors.quantity = 'Quantity must be a non-negative integer';
    }

    // Discount validation (optional field)
    if (formData.discount && formData.discount.trim() !== '') {
      // Allow various discount formats: "10%", "10", "$5", "5.00", etc.
      const discountValue = formData.discount.trim();
      if (discountValue.length > 50) {
        errors.discount = 'Discount description is too long (max 50 characters)';
      }
    }

    if (!formData.description) {
      errors.description = 'Description is required';
    }

    // Ensure stock field is present (it should be a boolean)
    if (formData.stock === undefined || formData.stock === null) {
      errors.stock = 'Stock status is required';
    }

    // Only require image for new products, not for edits
    if (!isEditMode && !mainImage && !mainImagePreview) {
      errors.image = 'Product image is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      console.log('Submitting form data:', formData);

      // Create FormData object for file upload
      const productFormData = new FormData();

      // Add text fields
      Object.keys(formData).forEach((key) => {
        // For boolean values, convert to string to ensure they're properly sent
        if (typeof formData[key] === 'boolean') {
          productFormData.append(key, formData[key].toString());
        } else {
          productFormData.append(key, formData[key]);
        }
      });

      // Ensure all required fields are included
      const formRequiredFields = ['name', 'type', 'price', 'quantity', 'description', 'stock', 'category'];
      formRequiredFields.forEach(field => {
        if (!productFormData.has(field)) {
          if (field === 'stock' && formData.stock !== undefined) {
            productFormData.append('stock', formData.stock.toString());
          } else if (formData[field] !== undefined) {
            productFormData.append(field, formData[field]);
          }
        }
      });

      // Add main image if exists
      if (mainImage) {
        productFormData.append('image', mainImage);
        console.log('Adding main image to form data');
      } else if (isEditMode && mainImagePreview) {
        // If in edit mode and there's a preview but no new image selected,
        // we need to keep the existing image by sending a flag to the backend
        // This tells the backend to keep the existing image
        productFormData.append('keepExistingImage', 'true');
        console.log('Keeping existing main image');
      }

      // Add additional images if exist
      if (additionalImages.length > 0) {
        additionalImages.forEach((image) => {
          productFormData.append('additionalImages', image);
        });
        console.log(
          `Adding ${additionalImages.length} additional images to form data`
        );
      }

      // Log form data for debugging
      console.log('Form data entries:');
      for (let pair of productFormData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
      }

      // Double-check that all required fields are present
      const requiredFields = ['name', 'type', 'price', 'quantity', 'description', 'stock', 'category', 'image'];
      const missingFields = [];

      requiredFields.forEach(field => {
        if (!productFormData.has(field) && !(field === 'image' && isEditMode)) {
          missingFields.push(field);
        }
      });

      if (missingFields.length > 0) {
        console.warn('Missing required fields:', missingFields);
        // For image field in edit mode, we need to check if we have an existing image
        if (missingFields.includes('image') && isEditMode && mainImagePreview) {
          console.log('Edit mode with existing image preview - this is OK');
          // Remove 'image' from missing fields if we're in edit mode and have a preview
          missingFields.splice(missingFields.indexOf('image'), 1);
        }

        // If we still have missing fields, add them with default values
        if (missingFields.length > 0) {
          missingFields.forEach(field => {
            if (field === 'stock') {
              productFormData.append(field, 'true');
            } else if (field === 'type') {
              productFormData.append(field, 'regular');
            } else if (field === 'category') {
              productFormData.append(field, 'Uncategorized');
            }
          });
        }
      }

      console.log('Sending request to API...');
      const response = isEditMode
        ? await updateProduct(id, productFormData)
        : await createProduct(productFormData);

      console.log('API response:', response);

      if (response.status === 'success') {
        setSnackbar({
          open: true,
          message: isEditMode
            ? `Product "${formData.name}" updated successfully`
            : `Product "${formData.name}" created successfully`,
          severity: 'success',
        });

        // Redirect after a short delay
        setTimeout(() => {
          navigate('/products');
        }, 1500);
      } else {
        setSnackbar({
          open: true,
          message: response.message || 'Failed to save product',
          severity: 'error',
        });
        setLoading(false);
      }
    } catch (error) {
      console.error('Error saving product:', error);

      // Ensure we have a string message to display
      let errorMessage = 'Failed to save product';

      if (error.response && error.response.data) {
        if (typeof error.response.data.message === 'string') {
          errorMessage = error.response.data.message;
        } else if (error.response.data.message) {
          // If it's an object, convert to string
          try {
            errorMessage = JSON.stringify(error.response.data.message);
          } catch (e) {
            errorMessage = 'Validation error occurred';
          }
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      setSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error',
      });
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/products');
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  if (loading && isEditMode) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/products')}
          sx={{ mr: 2 }}
        >
          Back to Products
        </Button>
        <Typography variant="h4">
          {isEditMode ? 'Edit Product' : 'Add New Product'}
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    label="Product Name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    fullWidth
                    error={!!formErrors.name}
                    helperText={formErrors.name}
                    disabled={loading}
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth error={!!formErrors.category} required>
                    <InputLabel>Category</InputLabel>
                    <Select
                      name="category"
                      value={formData.category}
                      label="Category"
                      onChange={handleInputChange}
                      disabled={loading}
                    >
                      {categories.map((category) => (
                        <MenuItem key={category} value={category}>
                          {category}
                        </MenuItem>
                      ))}
                    </Select>
                    {formErrors.category && (
                      <Typography variant="caption" color="error">
                        {formErrors.category}
                      </Typography>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Price ($)"
                    name="price"
                    value={formData.price}
                    onChange={handleNumberInputChange}
                    fullWidth
                    error={!!formErrors.price}
                    helperText={formErrors.price}
                    disabled={loading}
                    required
                    InputProps={{
                      startAdornment: <Typography sx={{ mr: 1 }}>$</Typography>,
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Discount"
                    name="discount"
                    value={formData.discount}
                    onChange={handleInputChange}
                    fullWidth
                    error={!!formErrors.discount}
                    helperText={formErrors.discount || 'Optional: e.g., "10%", "$5 off", "Buy 1 Get 1"'}
                    disabled={loading}
                    placeholder="e.g., 10%, $5 off, Buy 1 Get 1"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Quantity"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleNumberInputChange}
                    fullWidth
                    error={!!formErrors.quantity}
                    helperText={formErrors.quantity}
                    disabled={loading}
                    required
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Type</InputLabel>
                    <Select
                      name="type"
                      value={formData.type}
                      label="Type"
                      onChange={handleInputChange}
                      disabled={loading}
                    >
                      <MenuItem value="regular">Regular</MenuItem>
                      <MenuItem value="featured">Featured</MenuItem>
                      <MenuItem value="sale">Sale</MenuItem>
                      <MenuItem value="new">New</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.stock === undefined ? true : formData.stock}
                        onChange={handleInputChange}
                        name="stock"
                        disabled={loading}
                      />
                    }
                    label="In Stock"
                  />
                  {formErrors.stock && (
                    <Typography variant="caption" color="error" display="block">
                      {formErrors.stock}
                    </Typography>
                  )}
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    label="Description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    fullWidth
                    multiline
                    rows={4}
                    error={!!formErrors.description}
                    helperText={formErrors.description}
                    disabled={loading}
                    required
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom>
                Product Images
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Main Image
                </Typography>

                <Box
                  sx={{
                    border: '1px dashed grey',
                    borderRadius: 1,
                    p: 1,
                    mb: 1,
                    height: 200,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    position: 'relative',
                    overflow: 'hidden',
                  }}
                >
                  {mainImagePreview ? (
                    <Box
                      component="img"
                      src={mainImagePreview}
                      alt="Product preview"
                      sx={{
                        maxWidth: '100%',
                        maxHeight: '100%',
                        objectFit: 'contain',
                      }}
                    />
                  ) : (
                    <Typography color="text.secondary">
                      No image selected
                    </Typography>
                  )}
                </Box>

                <Button
                  component="label"
                  variant="outlined"
                  startIcon={<UploadIcon />}
                  fullWidth
                  disabled={loading}
                >
                  Upload Image
                  <input
                    type="file"
                    accept="image/*"
                    hidden
                    onChange={handleMainImageChange}
                  />
                </Button>

                {formErrors.image && (
                  <Typography variant="caption" color="error">
                    {formErrors.image}
                  </Typography>
                )}
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Additional Images
              </Typography>

              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {additionalImagePreviews.map((preview, index) => (
                  <Box
                    key={index}
                    sx={{
                      width: 80,
                      height: 80,
                      border: '1px solid #ddd',
                      borderRadius: 1,
                      position: 'relative',
                      overflow: 'hidden',
                    }}
                  >
                    <Box
                      component="img"
                      src={preview}
                      alt={`Additional image ${index + 1}`}
                      sx={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                      }}
                    />
                    <IconButton
                      size="small"
                      sx={{
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        bgcolor: 'rgba(255, 255, 255, 0.7)',
                        '&:hover': {
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                        },
                      }}
                      onClick={() => handleRemoveAdditionalImage(index)}
                      disabled={loading}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                ))}
              </Box>

              <Button
                component="label"
                variant="outlined"
                startIcon={<UploadIcon />}
                fullWidth
                disabled={loading}
              >
                Add More Images
                <input
                  type="file"
                  accept="image/*"
                  hidden
                  multiple
                  onChange={handleAdditionalImagesChange}
                />
              </Button>
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<CancelIcon />}
                  onClick={handleCancel}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<SaveIcon />}
                  disabled={loading}
                >
                  {loading ? (
                    <CircularProgress size={24} />
                  ) : isEditMode ? (
                    'Update Product'
                  ) : (
                    'Create Product'
                  )}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {typeof snackbar.message === 'string' ? snackbar.message : 'An error occurred'}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ProductForm;
